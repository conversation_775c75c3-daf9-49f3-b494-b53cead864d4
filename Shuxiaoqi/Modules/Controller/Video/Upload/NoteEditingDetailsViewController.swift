import UIKit
import HXPhotoPicker
import SnapKit
import CoreLocation

/// 笔记（图片）发布页，支持最多 9 张图片选择与编辑
class NoteEditingDetailsViewController: BaseViewController, UITextViewDelegate, CLLocationManagerDelegate {

    // MARK: - 图片数据
    private var selectedImages: [UIImage] = [] {
        didSet {
            collectionView.reloadData()
            updateImageCountIndicator()
        }
    }
    private let maxCount = 9

    // MARK: - 新增：外部图片传入支持
    private var initialImages: [UIImage]?
    
    /// 新增初始化方法，支持外部传入图片
    convenience init(images: [UIImage]?) {
        self.init()
        self.initialImages = images
    }

    // MARK: - UI
    private lazy var scrollView: UIScrollView = {
        let s = UIScrollView()
        s.showsVerticalScrollIndicator = true
        s.keyboardDismissMode = .onDrag
        return s
    }()
    private lazy var scrollContentView = UIView()

    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.itemSize = CGSize(width: 90, height: 90)
        layout.minimumInteritemSpacing = 2
        layout.minimumLineSpacing = 2
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .clear
        cv.showsHorizontalScrollIndicator = false
        cv.isScrollEnabled = true
        cv.register(ImageCell.self, forCellWithReuseIdentifier: "ImageCell")
        cv.register(AddCell.self, forCellWithReuseIdentifier: "AddCell")
        cv.dataSource = self
        cv.delegate = self
        return cv
    }()

    // 选中图片数量指示器
    private lazy var imageCountLabel: UILabel = {
        let lbl = UILabel()
        lbl.font = .systemFont(ofSize: 12)
        lbl.textColor = UIColor(hex: "#999999")
        lbl.textAlignment = .right
        lbl.text = "0/\(maxCount)张"
        return lbl
    }()

    private lazy var titleContainer: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor(hex: "#EDEDED")
        v.layer.cornerRadius = 8
        return v
    }()
    lazy var titleTextView: UITextView = {
        let tv = UITextView()
        tv.font = .systemFont(ofSize: 13)
        tv.backgroundColor = .clear
        tv.textContainerInset = UIEdgeInsets(top: 8, left: 0, bottom: 8, right: 0)
        tv.delegate = self
        tv.text = "请添加标题和描述能获得更多推荐"
        tv.textColor = UIColor(hex: "#999999")
        return tv
    }()
    private lazy var characterCountLabel: UILabel = {
        let lbl = UILabel()
        lbl.text = "0/100"
        lbl.font = .systemFont(ofSize: 12)
        lbl.textColor = UIColor(hex: "#999999")
        return lbl
    }()

    private lazy var settingsContainer = UIView()

    private var categoryTitles: [String] = ["请选择"]
    private var categoryIds: [Int] = [-1]
    private var selectedCategoryIndex: Int = 0
    private lazy var categoryStatusLabel = UILabel()

    private var privacySelectedIndex: Int = 0
    private lazy var privacyStatusLabel = UILabel()

    // 新增：封面选择状态标签
    private lazy var coverStatusLabel = UILabel()

    private lazy var commentSwitch: UISwitch = {
        let sw = UISwitch()
        sw.onTintColor = UIColor(hex: "#FF6236")
        sw.addTarget(self, action: #selector(commentSwitchChanged), for: .valueChanged)
        return sw
    }()
    private lazy var followersCommentSwitch: UISwitch = {
        let sw = UISwitch()
        sw.onTintColor = UIColor(hex: "#FF6236")
        return sw
    }()
    
    // MARK: - 定时发布控件
    private lazy var timedPublishSwitch: UISwitch = {
        let sw = UISwitch()
        sw.onTintColor = UIColor(hex: "#FF6236")
        sw.addTarget(self, action: #selector(timedPublishSwitchChanged(_:)), for: .valueChanged)
        return sw
    }()
    private lazy var publishTimeLabel: UILabel = {
        let lbl = UILabel()
        lbl.text = "yyyy-mm-dd hh:mm"
        lbl.font = .systemFont(ofSize: 14)
        lbl.textColor = UIColor(hex: "#999999")
        lbl.isHidden = true
        return lbl
    }()

    // 存草稿
    private lazy var draftButton: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor(hex: "#F5F5F5")
        let icon = UIImageView(image: UIImage(named: "icon_draft_bmbtn"))
        icon.contentMode = .scaleAspectFit
        v.addSubview(icon)
        icon.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.height.equalTo(28)
        }
        let label = UILabel()
        label.text = "存草稿"
        label.font = .systemFont(ofSize: 14, weight: .bold)
        label.textColor = UIColor(hex: "#BFBFBF")
        label.textAlignment = .center
        v.addSubview(label)
        label.snp.makeConstraints { make in
            make.top.equalTo(icon.snp.bottom).offset(3)
            make.centerX.equalToSuperview()
            make.bottom.lessThanOrEqualToSuperview()
        }

        let tap = UITapGestureRecognizer(target: self, action: #selector(saveDraftTapped))
        v.addGestureRecognizer(tap)
        return v
    }()

    // 渐变层：发布按钮背景
    private let publishGradientLayer = CAGradientLayer()

    // 发布笔记按钮
    private lazy var publishButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle("发布笔记", for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        btn.layer.cornerRadius = 24
        btn.layer.masksToBounds = true

        // 发布按钮渐变层
        publishGradientLayer.colors = [UIColor(hex: "#FF8D36").cgColor, UIColor(hex: "#FF5858").cgColor]
        publishGradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
        publishGradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
        publishGradientLayer.cornerRadius = 24
        btn.layer.insertSublayer(publishGradientLayer, at: 0)
        // 初始 frame，后续在 viewDidLayoutSubviews 更新
        publishGradientLayer.frame = CGRect(x: 0, y: 0, width: 280, height: 48)

        btn.addTarget(self, action: #selector(publishButtonTapped), for: .touchUpInside)
        return btn
    }()

    // MARK: - 位置相关属性
    private var locationSwitch: UISwitch?
    private var locationStatusLabel: UILabel?
    private var locationManager: CLLocationManager?
    private var currentCoordinate: CLLocationCoordinate2D?
    private var currentLocationString: String?
    private var currentProvince: String?
    private var currentCity: String?
    private var currentCityId: String?
    private var currentFullAddress: String?
    private var provinceList: [[String: Any]]?
    private var cityList: [[[String: Any]]]?
    private var cityJsonLoaded = false
    private let provinceJsonUrl = "https://image.gzyoushu.com/ae1e13622e464c2899f53858a2596b56.json"
    private let cityJsonUrl = "https://image.gzyoushu.com/dc571ca6b0cd498d91125ce5a1c02cb7.json"

    // MARK: - 拖拽排序相关（新增）
    private var longPressGesture: UILongPressGestureRecognizer? // 长按手势用于排序
    private var draggingIndexPath: IndexPath? // 当前拖拽的 item
    private var dragSnapshot: UIView? // 截图视图
    private var isDraggingToDeleteArea = false // 是否拖到删除区域
    private lazy var deleteAreaView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.red.withAlphaComponent(0.85)
        v.layer.cornerRadius = 12
        v.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        v.isHidden = true
        let label = UILabel()
        label.text = "拖到这里删除"
        label.font = .systemFont(ofSize: 15, weight: .medium)
        label.textColor = .white
        v.addSubview(label)
        label.snp.makeConstraints { $0.center.equalToSuperview() }
        return v
    }()

    // MARK: - 加载弹窗
    private var loadingAlert: UIAlertController?

    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        navBar.backgroundColor = UIColor(hex: "#F5F5F5")
        navTitle = "发布笔记"
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        setupUI()
        fetchCategories()
        
        // 点击背景关闭键盘
        let tap = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        tap.cancelsTouchesInView = false
        view.addGestureRecognizer(tap)
        
        // ✨ 启用拖拽排序
        setupDragAndDrop()
        
        // 新增：如果有外部传入图片，赋值给 selectedImages
        if let imgs = initialImages, !imgs.isEmpty {
            let limited = Array(imgs.prefix(maxCount))
            selectedImages = limited
        }
    }

    // MARK: - 拦截返回按钮
    override func backButtonTapped() {
        // 如有加载弹窗或正在上传时不允许退出
        guard loadingAlert == nil else { return }

        let popup = BackInterceptPopupView(frame: .zero)
        popup.onActionSelected = { [weak self] action in
            guard let self = self else { return }
            switch action {
            case .discard:
                if let nav = self.navigationController, nav.viewControllers.count > 1 {
                    nav.popViewController(animated: true)
                } else {
                    self.dismiss(animated: true)
                }
            case .saveDraft:
                self.saveDraftTapped()
            }
        }
        popup.present(below: self.backButton)
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        publishGradientLayer.frame = publishButton.bounds
    }

    private func setupUI() {
        contentView.addSubview(scrollView)
        scrollView.snp.makeConstraints { $0.edges.equalToSuperview() }
        
        scrollView.addSubview(scrollContentView)
        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(contentView)
        }

        scrollContentView.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            // 高度固定 90
            make.height.equalTo(90)
        }
        // 指示器
        scrollContentView.addSubview(imageCountLabel)
        imageCountLabel.snp.makeConstraints { make in
            make.top.equalTo(collectionView.snp.bottom).offset(4)
            make.right.equalTo(collectionView.snp.right)
        }
        updateCollectionViewHeight()

        // 仅保留输入框容器
        scrollContentView.addSubview(titleContainer)
        titleContainer.snp.makeConstraints { make in
            make.top.equalTo(collectionView.snp.bottom).offset(24)
            make.left.right.equalTo(view).inset(20)
            make.height.equalTo(120)
        }
        
        titleContainer.addSubview(titleTextView)
        titleTextView.snp.makeConstraints { make in
            make.top.left.equalToSuperview().offset(8)
            make.right.equalToSuperview().offset(-8)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        titleContainer.addSubview(characterCountLabel)
        characterCountLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-8)
            make.bottom.equalToSuperview().offset(-4)
        }
        
        scrollContentView.addSubview(settingsContainer)
        settingsContainer.snp.makeConstraints { make in
            make.top.equalTo(titleContainer.snp.bottom).offset(16)
            make.left.right.equalTo(view).inset(16)
        }
        setupSettingsOptions()

        // 底部按钮布局： draftButton(48*48) + spacing + publishButton(280*48)
        scrollContentView.addSubview(draftButton)
        scrollContentView.addSubview(publishButton)

        let buttonTopOffset: CGFloat = 24
        let buttonBottomOffset: CGFloat = -24
        let screenWidth = UIScreen.main.bounds.width
        let buttonWidths: CGFloat = 48 + 280
        var horizontalSpacing = (screenWidth - buttonWidths) / 3
        if horizontalSpacing < 16 { horizontalSpacing = 16 } // 最小间距保证

        draftButton.snp.makeConstraints { make in
            make.top.equalTo(settingsContainer.snp.bottom).offset(buttonTopOffset)
            make.left.equalToSuperview().offset(horizontalSpacing)
            make.width.height.equalTo(48)
            make.bottom.equalToSuperview().offset(buttonBottomOffset)
        }

        publishButton.snp.makeConstraints { make in
            make.centerY.equalTo(draftButton)
            make.left.equalTo(draftButton.snp.right).offset(horizontalSpacing)
            make.width.equalTo(280)
            make.height.equalTo(48)
            make.right.equalToSuperview().offset(-horizontalSpacing)
        }
    }

    private func updateCollectionViewHeight() {
        collectionView.snp.updateConstraints { make in
            make.height.equalTo(92)
        }
    }

    // MARK: - 上传逻辑
    @objc private func publishButtonTapped() {
        // 1. 校验
        guard !selectedImages.isEmpty else {
            showAlert(title: "提示", message: "请至少选择一张图片")
            return
        }
        let title = titleTextView.text.trimmingCharacters(in: .whitespacesAndNewlines)
        if title.isEmpty || title == "请添加标题" {
            showAlert(title: "提示", message: "请填写标题")
            return
        }
        let categoryId = categoryIds[selectedCategoryIndex]
        if categoryId == -1 {
            showAlert(title: "提示", message: "请选择分类")
            return
        }
        // 2. 组装参数
        var params: [String: Any] = [:]
        params["worksTitle"] = title
        params["worksCategoryId"] = categoryId
        params["privacy"] = privacySelectedIndex + 1
        params["allowComment"] = commentSwitch.isOn ? 1 : 0
        params["followComment"] = followersCommentSwitch.isOn ? 1 : 0
        // ... 其他参数
        print("准备上传 \(selectedImages.count) 张图片，参数：", params)
        // 3. 上传图片时显示加载弹窗
        showLoadingAlert(message: "正在上传图片...")
        uploadImages(selectedImages) { [weak self] imageUrls in
            guard let self = self else { return }
            if imageUrls.isEmpty {
                self.hideLoadingAlert()
                self.showAlert(title: "上传失败", message: "图片上传失败，请重试")
                return
            }
            // 4. 发布作品接口（对接视频发布页同一接口，worksType=2，图片）
            self.showLoadingAlert(message: "正在发布笔记...")
            self.publishNote(params: params, imageUrls: imageUrls)
        }
    }

    // MARK: - 图片上传逻辑（预留）
    /// 上传图片，返回图片URL数组
    /// 已对接 APIManager.shared.uploadFileQNRaw，上传逻辑与 ImageCropPreviewController 保持一致
    private func uploadImages(_ images: [UIImage], completion: @escaping ([String]) -> Void) {
        print("[上传] 开始上传图片，共\(images.count)张")
        let imageDatas = images.compactMap { $0.jpegData(compressionQuality: 0.9) }
        guard !imageDatas.isEmpty else {
            print("[上传] 图片数据为空")
            completion([])
            return
        }
        APIManager.shared.uploadFileQNRaw(files: imageDatas) { result in
            switch result {
            case .success(let response):
                print("[上传] 上传成功: \(response)")
                let urls = response.data?.flatMap { $0.data } ?? []
                completion(urls)
            case .failure(let error):
                print("[上传] 上传失败: \(error)")
                completion([])
            }
        }
    }

    // MARK: - 发布作品接口（对接视频发布页同一接口，worksType=2，图片）
    /// 发布笔记接口，params为参数，imageUrls为图片URL数组
    private func publishNote(params: [String: Any], imageUrls: [String]) {
        var finalParams = params
        finalParams["worksType"] = 2 // 2-图片
        finalParams["worksUrl"] = imageUrls // 多图
        finalParams["worksCoverImg"] = imageUrls.first ?? "" // 封面取第一张
        // 其他参数（如有）可补充
        print("[发布] 最终入参: \(finalParams)")
        APIManager.shared.uploadShortVideoWorks(params: finalParams) { [weak self] apiResult in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.hideLoadingAlert()
                switch apiResult {
                case .success(_):
                    self.showAlert(title: "发布成功", message: "您的笔记已发布！") {
                        self.navigateBackAfterPublish()
                    }
                case .failure(let error):
                    self.showAlert(title: "发布失败", message: error.errorMessage)
                }
            }
        }
    }

    // MARK: - 加载弹窗
    private func showLoadingAlert(message: String) {
        if loadingAlert != nil { return }
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.translatesAutoresizingMaskIntoConstraints = false
        alert.view.addSubview(indicator)
        NSLayoutConstraint.activate([
            indicator.centerXAnchor.constraint(equalTo: alert.view.centerXAnchor),
            indicator.bottomAnchor.constraint(equalTo: alert.view.bottomAnchor, constant: -20)
        ])
        indicator.startAnimating()
        present(alert, animated: true)
        loadingAlert = alert
    }
    private func hideLoadingAlert() {
        if let alert = loadingAlert {
            alert.dismiss(animated: true)
            loadingAlert = nil
        }
    }

    private func showAlert(title: String, message: String, completion: (() -> Void)? = nil) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            completion?()
        })
        present(alert, animated: true)
    }

    // MARK: - 拖拽排序逻辑 ↓↓↓
    private func setupDragAndDrop() {
        // 1. 长按手势
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(handleLongPress(_:)))
        longPress.minimumPressDuration = 0.35
        collectionView.addGestureRecognizer(longPress)
        self.longPressGesture = longPress
        
        // 2. 删除区域
        view.addSubview(deleteAreaView)
        let baseHeight: CGFloat = 60
        deleteAreaView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo((baseHeight + 20) + view.safeAreaInsets.bottom)
        }
    }
    
    private func showDeleteArea() {
        guard deleteAreaView.isHidden else { return }
        deleteAreaView.alpha = 0
        deleteAreaView.isHidden = false
        UIView.animate(withDuration: 0.25) {
            self.deleteAreaView.alpha = 1
        }
    }
    
    private func hideDeleteArea() {
        guard !deleteAreaView.isHidden else { return }
        UIView.animate(withDuration: 0.25, animations: {
            self.deleteAreaView.alpha = 0
        }) { _ in
            self.deleteAreaView.isHidden = true
            self.deleteAreaView.backgroundColor = UIColor.red.withAlphaComponent(0.85)
            self.isDraggingToDeleteArea = false
        }
    }
    
    @objc private func handleLongPress(_ gesture: UILongPressGestureRecognizer) {
        let locationInView = gesture.location(in: view)
        let locationInCollection = gesture.location(in: collectionView)
        
        switch gesture.state {
        case .began:
            guard let indexPath = collectionView.indexPathForItem(at: locationInCollection),
                  indexPath.item < selectedImages.count, // 不允许拖动 "添加" 按钮
                  let cell = collectionView.cellForItem(at: indexPath) else { return }
            draggingIndexPath = indexPath
            // 创建截图
            dragSnapshot = cell.snapshotView(afterScreenUpdates: true)
            if let snapshot = dragSnapshot {
                snapshot.frame = cell.convert(cell.bounds, to: view)
                view.addSubview(snapshot)
                cell.isHidden = true
                UIView.animate(withDuration: 0.25) {
                    snapshot.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
                    snapshot.alpha = 0.9
                }
            }
            showDeleteArea()
        case .changed:
            guard let snapshot = dragSnapshot else { return }
            snapshot.center = locationInView
            // 自动边缘滚动（横向）- 改进版本
            let edgeThreshold: CGFloat = 40
            let scrollStep: CGFloat = 4
            let collectionBounds = collectionView.bounds
            let contentSize = collectionView.contentSize
            let currentOffset = collectionView.contentOffset

            // 左边缘滚动：当拖拽位置接近左边缘且还有内容可以向左滚动时
            if locationInCollection.x < edgeThreshold && currentOffset.x > 0 {
                let newOffset = max(currentOffset.x - scrollStep, 0)
                collectionView.setContentOffset(CGPoint(x: newOffset, y: currentOffset.y), animated: false)
                // 同步更新snapshot位置，保持相对位置不变
                snapshot.center.x = locationInView.x
            }
            // 右边缘滚动：当拖拽位置接近右边缘且还有内容可以向右滚动时
            else if locationInCollection.x > collectionBounds.width - edgeThreshold &&
                    currentOffset.x + collectionBounds.width < contentSize.width {
                let maxOffset = contentSize.width - collectionBounds.width
                let newOffset = min(currentOffset.x + scrollStep, maxOffset)
                collectionView.setContentOffset(CGPoint(x: newOffset, y: currentOffset.y), animated: false)
                // 同步更新snapshot位置，保持相对位置不变
                snapshot.center.x = locationInView.x
            }
            // 是否进入删除区域
            let isInsideDelete = deleteAreaView.frame.contains(locationInView)
            if isInsideDelete && !isDraggingToDeleteArea {
                isDraggingToDeleteArea = true
                UIView.animate(withDuration: 0.2) {
                    self.deleteAreaView.backgroundColor = UIColor.red
                }
            } else if !isInsideDelete && isDraggingToDeleteArea {
                isDraggingToDeleteArea = false
                UIView.animate(withDuration: 0.2) {
                    self.deleteAreaView.backgroundColor = UIColor.red.withAlphaComponent(0.85)
                }
            }
            // 目标索引
            if let fromIndexPath = draggingIndexPath,
               let toIndexPath = collectionView.indexPathForItem(at: locationInCollection),
               toIndexPath.item < selectedImages.count,
               toIndexPath != fromIndexPath {
                // 更新数据源
                collectionView.performBatchUpdates({
                    let moved = selectedImages.remove(at: fromIndexPath.item)
                    selectedImages.insert(moved, at: toIndexPath.item)
                    // collectionView 移动 cell
                    collectionView.moveItem(at: fromIndexPath, to: toIndexPath)
                    draggingIndexPath = toIndexPath
                }, completion: nil)
            }
        case .ended, .cancelled:
            hideDeleteArea()
            guard let startIndexPath = draggingIndexPath,
                  let cell = collectionView.cellForItem(at: startIndexPath) else {
                cleanupDrag()
                return
            }
            // 删除
            if isDraggingToDeleteArea {
                selectedImages.remove(at: startIndexPath.item)
                cleanupDrag()
                return
            }
            // 尝试放置到新的 indexPath
            if let destIndexPath = collectionView.indexPathForItem(at: locationInCollection), destIndexPath.item < selectedImages.count {
                if destIndexPath != startIndexPath {
                    let moved = selectedImages.remove(at: startIndexPath.item)
                    selectedImages.insert(moved, at: destIndexPath.item)
                    collectionView.reloadData()
                }
            }
            // 复位动画
            if let snapshot = dragSnapshot {
                UIView.animate(withDuration: 0.25, animations: {
                    snapshot.transform = .identity
                    snapshot.frame = cell.convert(cell.bounds, to: self.view)
                }) { _ in
                    cell.isHidden = false
                    snapshot.removeFromSuperview()
                    self.cleanupDrag()
                }
            } else {
                cleanupDrag()
            }
        default:
            break
        }
    }
    
    private func cleanupDrag() {
        dragSnapshot?.removeFromSuperview()
        dragSnapshot = nil
        // 1. 取消隐藏
        if let idx = draggingIndexPath, let cell = collectionView.cellForItem(at: idx) {
            cell.isHidden = false
        }
        // 确保屏幕外的被隐藏 cell 恢复正常
        for cell in collectionView.visibleCells where cell.isHidden {
            cell.isHidden = false
        }
        // 2. 刷新数据，彻底消除占位空白
        collectionView.reloadData()
        draggingIndexPath = nil
        isDraggingToDeleteArea = false
    }

    private func updateImageCountIndicator() {
        imageCountLabel.text = "\(selectedImages.count)/\(maxCount)张"
    }

    // MARK: - 发布成功后返回
    private func navigateBackAfterPublish() {
        if let nav = self.navigationController {
            if nav.presentingViewController != nil {
                nav.dismiss(animated: true)
                return
            }
            nav.popViewController(animated: true)
            return
        }
        self.dismiss(animated: true)
    }

    @objc private func saveDraftTapped() {
        // 1. 校验：草稿仅需图片 URL，因此只确保至少选择了一张图片
        guard !selectedImages.isEmpty else {
            showAlert(title: "提示", message: "请至少选择一张图片")
            return
        }

        // 2. 组装可选参数（服务端除 URL 之外其余均为非必填）
        var params: [String: Any] = [:]
        let title = titleTextView.text.trimmingCharacters(in: .whitespacesAndNewlines)
        if !title.isEmpty, title != "请添加标题" {
            params["worksTitle"] = title
        }
        let categoryId = categoryIds[selectedCategoryIndex]
        if categoryId != -1 {
            params["worksCategoryId"] = categoryId
        }
        // 其余字段按当前 UI 状态填充，均为可选
        params["privacy"] = privacySelectedIndex + 1
        params["allowComment"] = commentSwitch.isOn ? 1 : 0
        params["followComment"] = followersCommentSwitch.isOn ? 1 : 0

        // 3. 上传图片
        showLoadingAlert(message: "正在上传图片...")
        uploadImages(selectedImages) { [weak self] imageUrls in
            guard let self = self else { return }
            if imageUrls.isEmpty {
                self.hideLoadingAlert()
                self.showAlert(title: "上传失败", message: "图片上传失败，请重试")
                return
            }
            // 4. 保存草稿
            self.showLoadingAlert(message: "正在保存草稿...")
            self.saveDraft(params: params, imageUrls: imageUrls)
        }
    }

    // MARK: - 保存草稿接口调用
    /// 保存作品草稿流程，与发布作品参数一致，仅接口不同
    private func saveDraft(params: [String: Any], imageUrls: [String]) {
        var finalParams = params
        finalParams["worksType"] = 2 // 2-图片
        finalParams["worksUrl"] = imageUrls
        finalParams["worksCoverImg"] = imageUrls.first ?? ""
        
        APIManager.shared.saveVideoWorksDrafts(params: finalParams) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.hideLoadingAlert()
                switch result {
                case .success(_):
                    self.showAlert(title: "保存成功", message: "草稿已保存！") {
                        self.navigateBackAfterPublish()
                    }
                case .failure(let error):
                    self.showAlert(title: "保存失败", message: error.errorMessage)
                }
            }
        }
    }
}

// MARK: - CollectionView
extension NoteEditingDetailsViewController: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return min(selectedImages.count + 1, maxCount)
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if indexPath.item == selectedImages.count && selectedImages.count < maxCount {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "AddCell", for: indexPath) as! AddCell
            return cell
        } else {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ImageCell", for: indexPath) as! ImageCell
            cell.configure(with: selectedImages[indexPath.item])
            cell.onDelete = { [weak self] in
                guard let self = self else { return }
                self.selectedImages.remove(at: indexPath.item)
            }
            return cell
        }
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if indexPath.item == selectedImages.count && selectedImages.count < maxCount {
            openImagePicker()
        } else {
            previewImage(at: indexPath.item)
        }
    }
}

// MARK: - Image Picker
extension NoteEditingDetailsViewController {
    private func openImagePicker() {
        var config = PickerConfiguration()
        config.selectMode = .multiple
        config.selectOptions = .photo
        config.maximumSelectedCount = maxCount - selectedImages.count
        config.modalPresentationStyle = .fullScreen
        config.editor.modalPresentationStyle = .fullScreen
        
        // 由于 HXPhotoPicker 新版本采用 async/await 及 "hx.present" 方式，这里改用 hx.present 进行调用
        self.hx.present(
            picker: config,
            finish: { [weak self] result, _ in
                guard let self = self else { return }
                // 将选择结果转换为 UIImage 数组并追加到已选图片中
                result.getImage { images in
                    self.selectedImages.append(contentsOf: images)
                }
            },
            cancel: nil
        )
    }

    private func previewImage(at index: Int) {
        let vc = ImagePreviewController(images: selectedImages, startIndex: index) { [weak self] idx in
            self?.selectedImages.remove(at: idx)
        }
        vc.modalPresentationStyle = .fullScreen
        present(vc, animated: true)
    }
}

// MARK: - Settings UI
extension NoteEditingDetailsViewController {
    
    private func setupSettingsOptions() {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.distribution = .fillEqually
        settingsContainer.addSubview(stackView)
        stackView.snp.makeConstraints { $0.edges.equalToSuperview() }

        let rowHeight: CGFloat = 48
        
        // 0. 选择封面（隐藏/注释）
        /*
        let coverView = createSettingRow(title: "选择封面", initialValue: "请选择", showArrow: true)
        coverView.snp.makeConstraints { $0.height.equalTo(rowHeight) }
        let coverTap = UITapGestureRecognizer(target: self, action: #selector(coverSelectorTapped))
        coverView.addGestureRecognizer(coverTap)
        coverStatusLabel = coverView.viewWithTag(101) as! UILabel
        stackView.addArrangedSubview(coverView)
        */

        // 分类选择cell恢复原有逻辑
        let categoryView = createSettingRow(title: "选择分类", initialValue: "请选择", showArrow: true)
        categoryView.snp.makeConstraints { $0.height.equalTo(rowHeight) }
        let categoryTap = UITapGestureRecognizer(target: self, action: #selector(categorySelectorTapped))
        categoryView.addGestureRecognizer(categoryTap)
        categoryStatusLabel = categoryView.viewWithTag(101) as! UILabel
        stackView.addArrangedSubview(categoryView)

        // 位置
        let locationView = createSettingRow(title: "添加位置", initialValue: "未添加", showArrow: false)
        locationView.snp.makeConstraints { $0.height.equalTo(rowHeight) }
        // 开关
        let locSwitch = UISwitch()
        locSwitch.onTintColor = UIColor(hex: "#FF6236")
        locSwitch.addTarget(self, action: #selector(locationSwitchChanged(_:)), for: .valueChanged)
        locationView.addSubview(locSwitch)
        locSwitch.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }
        locationSwitch = locSwitch
        // 状态标签重约束到开关左侧
        if let lbl = locationView.viewWithTag(101) as? UILabel {
            locationStatusLabel = lbl
            lbl.snp.remakeConstraints { make in
                make.right.equalTo(locSwitch.snp.left).offset(-12)
                make.centerY.equalToSuperview()
            }
        }

        let privacyView = createSettingRow(title: "谁可以看", initialValue: "公开", showArrow: true)
        privacyView.snp.makeConstraints { $0.height.equalTo(rowHeight) }
        let privacyTap = UITapGestureRecognizer(target: self, action: #selector(privacySelectorTapped))
        privacyView.addGestureRecognizer(privacyTap)
        privacyStatusLabel = privacyView.viewWithTag(101) as! UILabel
        
        let commentsView = createSettingRowWithSwitch(title: "允许评论", switch: commentSwitch)
        commentsView.snp.makeConstraints { $0.height.equalTo(rowHeight) }

        let followersCommentView = createSettingRowWithSwitch(title: "仅关注的人可评论", switch: followersCommentSwitch)
        followersCommentView.snp.makeConstraints { $0.height.equalTo(rowHeight) }
        followersCommentSwitch.isEnabled = commentSwitch.isOn
        followersCommentSwitch.alpha = commentSwitch.isOn ? 1.0 : 0.5
        
        // 6. 定时发布
        let timedPublishView = createSettingRow(title: "定时发布", initialValue: "", showArrow: false)
        timedPublishView.snp.makeConstraints { $0.height.equalTo(rowHeight) }
        timedPublishView.addSubview(timedPublishSwitch)
        timedPublishSwitch.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }
        timedPublishView.addSubview(publishTimeLabel)
        publishTimeLabel.snp.makeConstraints { make in
            make.right.equalTo(timedPublishSwitch.snp.left).offset(-12)
            make.centerY.equalToSuperview()
        }
        publishTimeLabel.isUserInteractionEnabled = true
        let timeTap = UITapGestureRecognizer(target: self, action: #selector(publishTimeLabelTapped))
        publishTimeLabel.addGestureRecognizer(timeTap)

//        stackView.addArrangedSubview(coverView)
        stackView.addArrangedSubview(categoryView)
        stackView.addArrangedSubview(locationView)
        stackView.addArrangedSubview(privacyView)
        stackView.addArrangedSubview(commentsView)
        stackView.addArrangedSubview(followersCommentView)
        stackView.addArrangedSubview(timedPublishView)
    }

    private func createSettingRow(title: String, initialValue: String, showArrow: Bool) -> UIView {
        let view = UIView()
        view.isUserInteractionEnabled = true
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        view.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
        }

        if showArrow {
            let arrow = UIImageView(image: UIImage(named: "setting_arrow") ?? UIImage(systemName: "chevron.right"))
            arrow.tintColor = .lightGray
            view.addSubview(arrow)
            arrow.snp.makeConstraints { make in
                make.right.equalToSuperview().offset(-16)
                make.centerY.equalToSuperview()
                make.width.height.equalTo(16)
            }
        }
        
        let statusLabel = UILabel()
        statusLabel.text = initialValue
        statusLabel.font = .systemFont(ofSize: 14)
        statusLabel.textColor = UIColor(hex: "#999999")
        statusLabel.textAlignment = .right
        statusLabel.tag = 101 // for easy access
        view.addSubview(statusLabel)
        statusLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(showArrow ? -36 : -16)
            make.centerY.equalToSuperview()
        }

        let separator = createSeparator()
        view.addSubview(separator)
        separator.snp.makeConstraints { $0.left.bottom.right.equalToSuperview() }
        
        return view
    }
    
    private func createSettingRowWithSwitch(title: String, switch control: UISwitch) -> UIView {
        let view = UIView()
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        view.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
        }
        
        view.addSubview(control)
        control.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }
        
        let separator = createSeparator()
        view.addSubview(separator)
        separator.snp.makeConstraints { $0.left.bottom.right.equalToSuperview() }
        
        return view
    }
    
    private func createSeparator() -> UIView {
        let separator = UIView()
        separator.backgroundColor = UIColor(hex: "#EEEEEE")
        separator.snp.makeConstraints { $0.height.equalTo(0.5) }
        return separator
    }

    @objc private func categorySelectorTapped() {
        dismissKeyboard()
        guard !categoryTitles.isEmpty else {
            showAlert(title: "提示", message: "暂无可选分类，请稍后重试")
            return
        }
        let popup = CategoryPickerPopupView(categories: categoryTitles, selectedIndex: selectedCategoryIndex)
        popup.onCancel = { }
        popup.onConfirm = { [weak self] idx in
            guard let self = self else { return }
            self.selectedCategoryIndex = idx
            self.categoryStatusLabel.text = self.categoryTitles[idx]
            self.categoryStatusLabel.textColor = (idx == 0) ? UIColor(hex: "#999999") : UIColor(hex: "#333333")
        }
        UIApplication.shared.keyWindow?.addSubview(popup)
    }
    
    @objc private func privacySelectorTapped() {
        dismissKeyboard()
        let selector = VisiblePopSelector(selectedIndex: privacySelectedIndex) { [weak self] idx in
            guard let self = self else { return }
            self.privacySelectedIndex = idx
            let options = ["所有人可见", "互相关注的人可见", "仅自己可见"]
            self.privacyStatusLabel.text = options[idx]
            self.privacyStatusLabel.textColor = UIColor(hex: "#333333")
        }
        selector.show(in: self.view)
    }
    
    @objc private func commentSwitchChanged(_ sender: UISwitch) {
        followersCommentSwitch.isEnabled = sender.isOn
        UIView.animate(withDuration: 0.2) {
            self.followersCommentSwitch.alpha = sender.isOn ? 1.0 : 0.5
        }
        if !sender.isOn {
            followersCommentSwitch.setOn(false, animated: true)
        }
    }
    
    @objc func dismissKeyboard() {
        view.endEditing(true)
    }

    private func fetchCategories() {
        // 调用真实API获取分类列表，与视频页一致
        APIManager.shared.getVideoTypeList(isHomeShow: false) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }
                switch result {
                case .success(let response):
                    guard response.isSuccess, let data = response.data else {
                        self.categoryTitles = ["请选择"]
                        self.categoryIds = [-1]
                        self.selectedCategoryIndex = 0
                        self.categoryStatusLabel.text = "请选择"
                        self.categoryStatusLabel.textColor = UIColor(hex: "#999999")
                        return
                    }
                    var titles = data.compactMap { $0.typeName }
                    var ids = data.compactMap { $0.id }
                    titles.insert("请选择", at: 0)
                    ids.insert(-1, at: 0)
                    self.categoryTitles = titles
                    self.categoryIds = ids
                    self.selectedCategoryIndex = 0
                    self.categoryStatusLabel.text = "请选择"
                    self.categoryStatusLabel.textColor = UIColor(hex: "#999999")
                case .failure(_):
                    self.categoryTitles = ["请选择"]
                    self.categoryIds = [-1]
                    self.selectedCategoryIndex = 0
                    self.categoryStatusLabel.text = "请选择"
                    self.categoryStatusLabel.textColor = UIColor(hex: "#999999")
                }
            }
        }
    }
    
    func textViewDidChange(_ textView: UITextView) {
        let count = textView.text.count
        characterCountLabel.text = "\(count)/100"
        if count > 100 {
            textView.text = String(textView.text.prefix(100))
            characterCountLabel.text = "100/100"
        }
    }
    
    func textViewDidBeginEditing(_ textView: UITextView) {
        if textView.textColor == UIColor(hex: "#999999") {
            textView.text = ""
            textView.textColor = UIColor(hex: "#333333")
        }
    }
    
    func textViewDidEndEditing(_ textView: UITextView) {
        if textView.text.isEmpty {
            textView.text = "请添加标题和描述能获得更多推荐"
            textView.textColor = UIColor(hex: "#999999")
            characterCountLabel.text = "0/100"
        }
    }

    @objc private func coverSelectorTapped() {
        // 封面选择逻辑：可从已选图片中选择，也可以重新选择
        print("选择封面")
        guard !selectedImages.isEmpty else {
            showAlert(title: "提示", message: "请先选择图片")
            return
        }
        // 简易实现：使用第 1 张图片作为封面
        coverStatusLabel.text = "已选择"
        coverStatusLabel.textColor = UIColor(hex: "#333333")
        // 若需弹出预览供用户选择，可在此处实现自定义选择器
    }

    // MARK: - 定时发布交互
    @objc private func timedPublishSwitchChanged(_ sender: UISwitch) {
        publishTimeLabel.isHidden = !sender.isOn
        if sender.isOn { showDateTimePicker() }
    }

    @objc private func publishTimeLabelTapped() { showDateTimePicker() }

    private func showDateTimePicker() {
        let picker = UIDatePicker()
        picker.datePickerMode = .dateAndTime
        picker.preferredDatePickerStyle = .wheels
        picker.minimumDate = Date()
        let alert = UIAlertController(title: "\n\n\n\n\n\n\n\n\n\n", message: nil, preferredStyle: .actionSheet)
        alert.view.addSubview(picker)
        picker.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(8)
        }
        let done = UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            let fmt = DateFormatter()
            fmt.dateFormat = "yyyy-MM-dd HH:mm"
            self?.publishTimeLabel.text = fmt.string(from: picker.date)
            self?.publishTimeLabel.isHidden = false
        }
        let cancel = UIAlertAction(title: "取消", style: .cancel) { [weak self] _ in
            self?.timedPublishSwitch.setOn(false, animated: true)
            self?.publishTimeLabel.isHidden = true
        }
        alert.addAction(done)
        alert.addAction(cancel)
        present(alert, animated: true)
    }

    // MARK: - 位置开关逻辑
    @objc private func locationSwitchChanged(_ sender: UISwitch) {
        dismissKeyboard()
        if sender.isOn {
            // 优先缓存定位
            if locationManager == nil {
                locationManager = CLLocationManager()
                locationManager?.delegate = self
            }
            if let cached = locationManager?.location {
                reverseGeocodeAndShow(location: cached)
            } else {
                locationStatusLabel?.text = "定位中..."
            }
            startLocationQuick()
        } else {
            locationStatusLabel?.text = "未添加"
        }
    }

    private func startLocationQuick() {
        if locationManager == nil {
            locationManager = CLLocationManager()
            locationManager?.delegate = self
        }
        let status = CLLocationManager.authorizationStatus()
        if status == .notDetermined {
            locationManager?.requestWhenInUseAuthorization()
        } else if status == .authorizedWhenInUse || status == .authorizedAlways {
            locationManager?.requestLocation()
        } else {
            showAlert(title: "无法获取定位", message: "请在设置中开启定位权限")
        }
    }

    private func reverseGeocodeAndShow(location: CLLocation) {
        let geocoder = CLGeocoder()
        geocoder.reverseGeocodeLocation(location) { [weak self] placemarks, error in
            guard let self = self else { return }
            if let _ = error {
                self.locationStatusLabel?.text = "未添加"
                return
            }
            guard let pm = placemarks?.first else {
                self.locationStatusLabel?.text = "未添加"
                return
            }
            let province = pm.administrativeArea ?? ""
            let city = pm.locality ?? pm.subAdministrativeArea ?? ""
            let display = "\(province) \(city)"
            self.locationStatusLabel?.text = display
            self.currentCoordinate = location.coordinate
            self.currentProvince = province
            self.currentCity = city
            self.currentLocationString = display
            // 完整地址
            let district = pm.subLocality ?? ""
            let thoroughfare = pm.thoroughfare ?? ""
            let subThoroughfare = pm.subThoroughfare ?? ""
            let name = pm.name ?? ""
            let detail = [province, city, district, thoroughfare, subThoroughfare, name].filter { !$0.isEmpty }.joined()
            self.currentFullAddress = detail.isEmpty ? display : detail

            self.fetchProvinceAndCityJsonIfNeeded {
                self.matchCityIdByProvinceAndCity()
            }
        }
    }

    private func fetchProvinceAndCityJsonIfNeeded(completion: @escaping () -> Void) {
        if cityJsonLoaded { completion(); return }
        let group = DispatchGroup()
         group.enter()
        URLSession.shared.dataTask(with: URL(string: provinceJsonUrl)!) { data, _, _ in
            if let data = data, let arr = try? JSONSerialization.jsonObject(with: data) as? [[String: Any]] {
                self.provinceList = arr
            }
            group.leave()
        }.resume()
        group.enter()
        URLSession.shared.dataTask(with: URL(string: cityJsonUrl)!) { data, _, _ in
            if let data = data, let arr = try? JSONSerialization.jsonObject(with: data) as? [[[String: Any]]] {
                self.cityList = arr
            }
            group.leave()
        }.resume()
        group.notify(queue: .main) {
            self.cityJsonLoaded = true
            completion()
        }
    }

    private func matchCityIdByProvinceAndCity() {
        guard let province = currentProvince, let city = currentCity, let provinceList = provinceList, let cityList = cityList else { return }
        guard let provinceIndex = provinceList.firstIndex(where: { ($0["label"] as? String)?.contains(province) == true }) else { return }
        let cities = cityList[provinceIndex]
        if let cityObj = cities.first(where: { ($0["label"] as? String)?.contains(city) == true }) {
            currentCityId = cityObj["value"] as? String
        }
    }

    // CLLocationManagerDelegate
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        if let loc = locations.first {
            reverseGeocodeAndShow(location: loc)
        }
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        locationStatusLabel?.text = "未添加"
    }

    // MARK: - 辅助
    private func findCategoryTextLabel() -> UILabel? {
        for sub in settingsContainer.subviews {
            for lbl in sub.subviews where lbl is UILabel {
                let label = lbl as! UILabel
                if label.text == "请选择" || categoryTitles.contains(label.text ?? "") {
                    return label
                }
            }
        }
        return nil
    }
}

// MARK: - Cells
private class ImageCell: UICollectionViewCell {
    private let imageView = UIImageView()
    private let deleteButton = UIButton(type: .custom)
    var onDelete: (() -> Void)?
    override init(frame: CGRect) {
        super.init(frame: frame)
        let imageSize: CGFloat = 80
        contentView.addSubview(imageView)
        imageView.layer.cornerRadius = 8
        imageView.clipsToBounds = true
        imageView.contentMode = .scaleAspectFill
        // 图片居中
        imageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSize(width: imageSize, height: imageSize))
        }
        // 使用icon_video_edit_image_clear
        deleteButton.setImage(UIImage(named: "icon_video_edit_image_clear"), for: .normal)
        deleteButton.backgroundColor = .white
        deleteButton.layer.cornerRadius = 10
        deleteButton.clipsToBounds = true
        // 可选：添加阴影
        deleteButton.layer.shadowColor = UIColor.black.withAlphaComponent(0.15).cgColor
        deleteButton.layer.shadowOffset = CGSize(width: 0, height: 1)
        deleteButton.layer.shadowOpacity = 1
        deleteButton.layer.shadowRadius = 2
        deleteButton.addTarget(self, action: #selector(deleteTapped), for: .touchUpInside)
        contentView.addSubview(deleteButton)
        // 按钮24x24，中心在图片右上角
        deleteButton.snp.makeConstraints { make in
            make.size.equalTo(CGSize(width: 20, height: 20))
            make.centerX.equalTo(imageView.snp.right).offset(-2)
            make.centerY.equalTo(imageView.snp.top).offset(3)
        }
    }
    required init?(coder: NSCoder) { fatalError() }
    func configure(with image: UIImage) {
        imageView.image = image
    }
    @objc private func deleteTapped() {
        onDelete?()
    }
    override func prepareForReuse() {
        super.prepareForReuse()
        isHidden = false
    }
}

private class AddCell: UICollectionViewCell {
    override init(frame: CGRect) {
        super.init(frame: frame)
        // 使用自定义图标 "icon_image_add" 作为添加图片按钮的中心图标
        let plus = UIImageView(image: UIImage(named: "icon_image_add") ?? UIImage(systemName: "plus"))
        plus.contentMode = .scaleAspectFit
        contentView.addSubview(plus)
        plus.snp.makeConstraints { $0.center.equalToSuperview() }
        contentView.layer.borderWidth = 1
        contentView.layer.borderColor = UIColor(hex: "#CCCCCC").cgColor
        contentView.layer.cornerRadius = 8
    }
    required init?(coder: NSCoder) { fatalError() }
}

// MARK: - 简易预览控制器
private class ImagePreviewController: UIViewController {
    private let images: [UIImage]
    private var currentIndex: Int
    private let deleteHandler: (Int) -> Void
    private let imageView = UIImageView()
    init(images: [UIImage], startIndex: Int, deleteHandler: @escaping (Int) -> Void) {
        self.images = images
        self.currentIndex = startIndex
        self.deleteHandler = deleteHandler
        super.init(nibName: nil, bundle: nil)
    }
    required init?(coder: NSCoder) { fatalError() }
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .black
        imageView.contentMode = .scaleAspectFit
        view.addSubview(imageView)
        imageView.snp.makeConstraints { $0.edges.equalToSuperview() }
        imageView.image = images[currentIndex]

        let close = UIButton(type: .system)
        close.setTitle("关闭", for: .normal)
        close.tintColor = .white
        close.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        view.addSubview(close)
        close.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(20)
            make.left.equalToSuperview().offset(20)
        }

        let delete = UIButton(type: .system)
        delete.setTitle("删除", for: .normal)
        delete.tintColor = .red
        delete.addTarget(self, action: #selector(deleteTapped), for: .touchUpInside)
        view.addSubview(delete)
        delete.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(20)
            make.right.equalToSuperview().offset(-20)
        }
    }
    @objc private func closeTapped() { dismiss(animated: true) }
    @objc private func deleteTapped() {
        deleteHandler(currentIndex)
        dismiss(animated: true)
    }
} 
